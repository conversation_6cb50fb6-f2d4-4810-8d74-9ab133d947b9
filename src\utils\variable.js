import heroImg from "../images/hero-bg.jpg";
import portfolio from "../images/portfolio.webp";

export const items = [
  "JavaScript",
  "TypeScript",
  "HTML5",
  "CSS3",
  "React",
  "Webpack",
  "JQuery",
  "Git",
  "BEM",
  "SASS",
  "Next JS",
  "Node JS",
  "Vue JS",
];
export const options = {
  size: "30px",
  color: "#858e99",
};

export const aboutText = `I'm a passionate Frontend with three years of experience turning ideas into reality and still counting.
The thrill of bringing a static design to life with code is what gets me going every morning. I'm a passionate front-end developer who thrives on collaboration and turning your ideas into something truly impactful.
`;

export const skillText = `Master of Modern Web Development: Crafting dynamic, visually stunning interfaces with a strong foundation in HTML, CSS, and JavaScript. Proficient in advanced technologies like React and TypeScript, with a knack for efficient styling using SCSS, Bootstrap, and Tailwind. Skilled in seamless version control with Git. Excels in communication, problem-solving, teamwork, and time management, all while infusing projects with a sense of humor.
Most important, I have got passion for learning and teaching, i believe the sky is the limit`;

export const projectsData = [
  {
    type: "bg1",
    value: "16 <span>| 24</span>",
    label: "Recipe App",
    details:
      "Vanilla JS project with a focus on creating a visually appealing and interactive user interface. Features include recipe search, ingredient filtering, and meal planning capabilities.",
    stacks: ["HTML5", "CSS3", "JavaScript", "Local Storage"],
    gitLink: "https://github.com/cursorbot/cursorbot",
    demoLink: "https://recipe-app-demo.netlify.app",
    image: heroImg,
    stats: {
      year: "2024",
      duration: "3 weeks",
      status: "Completed",
    },
  },
  {
    type: "bg1",
    value: '<i class="fas fa-battery-three-quarters"></i>',
    label: "Health Tracker",
    details:
      "A comprehensive web application that allows users to track their breathing patterns and monitor their respiratory health with real-time data visualization and progress tracking.",
    stacks: ["HTML5", "CSS3", "JavaScript", "Webpack", "Chart.js"],
    gitLink: "https://github.com/cursorbot/cursorbot",
    demoLink: "https://health-tracker-demo.netlify.app",
    image: portfolio,

    stats: {
      year: "2024",
      duration: "4 weeks",
      status: "Active",
    },
  },
  {
    type: "bg2",
    value: '<i class="fas fa-running"></i>',
    label: "React App",
    details:
      "A modern React application built with hooks and context API, featuring responsive design and optimized performance for seamless user experience.",
    stacks: ["React", "JavaScript", "CSS3", "Webpack", "Context API"],
    gitLink: "https://github.com/cursorbot/cursorbot",
    demoLink: "https://react-app-demo.netlify.app",
    image: heroImg,
    stats: {
      year: "2024",
      duration: "5 weeks",
      status: "Completed",
    },
  },
  {
    type: "bg1",
    value: "36 &deg;",
    label: "Fullstack Recipe App",
    details:
      "A fullstack web application that allows to create and vue your favorite recipes.",
    stacks: ["HTML5", "CSS3", "JavaScript", "Webpack", "VueJS"],
    gitLink: "https://github.com/cursorbot/cursorbot",
    image: heroImg,
  },
  {
    type: "bg1",
    value: '<i class="fas fa-bed"></i>',
    label: "Todo note app",
    details:
      "Demostrate how to use the HTML5 and CSS3 to create a responsive layout.",
    stacks: ["HTML5", "CSS3", "JavaScript"],
    gitLink: "https://github.com/cursorbot/cursorbot",
    image: heroImg,
  },
  {
    type: "bg2",
    value: "98 <span>bpm</span>",
    label: "Portfolio template",
    details:
      "crafted like a js framework single page application without using any framework.",
    stacks: ["HTML5", "CSS3", "JavaScript"],
    gitLink: "https://github.com/cursorbot/cursorbot",
    image: heroImg,
  },
];
