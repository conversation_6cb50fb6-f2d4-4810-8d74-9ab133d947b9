/* Enhanced Project Cards Styling */
.project__section .container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  width: 100%;
  margin-top: 3rem;
  padding: 0 1rem;
}

.project__section h2 {
  font-size: clamp(2rem, 4vw, 2.8rem);
  font-weight: 400;
  margin: 0.5rem;
  color: var(--color-secondary-dark);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.02em;
}

.project__section h2 span {
  font-size: clamp(1.5rem, 3vw, 2rem);
  color: var(--color-primary);
  font-weight: 300;
  opacity: 0.8;
}

.project__section p {
  font-size: clamp(0.9rem, 1.5vw, 1rem);
  color: var(--color-secondary-text);
  margin-top: 0.5rem;
  font-weight: 500;
}

.project__section .container > div {
  position: relative;
  cursor: pointer;
  height: 220px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0;
  animation: projectCardReveal 1s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

/* Ensure content is visible over background images */
.project__section .container > div::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.3) 100%
  );
  border-radius: 12px;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.project__section .container > div:hover::before {
  opacity: 0.7;
}

.project__section .container > div:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 0 20px 40px rgba(84, 98, 255, 0.2),
    0 8px 32px rgba(84, 98, 255, 0.15);
  border: 1px solid rgba(84, 98, 255, 0.3);
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.85) 0%,
    rgba(84, 98, 255, 0.9) 100%
  );
  color: var(--overlay-text);
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border-radius: 12px;
}

span.overlay-stack {
  margin: 0.25rem;
  font-size: 0.75rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-weight: 500;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  animation: stackTagBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
  animation-delay: calc(var(--i) * 0.1s + 0.3s);
  opacity: 0;
  transform: translateY(30px) scale(0.5);
}

.overlay-description {
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
  text-align: center;
  line-height: 1.5;
  font-weight: 400;
  opacity: 0.95;
}

.project__section .container > div:hover .overlay {
  opacity: 1;
  visibility: visible;
}

.project__section .container > div:hover span.overlay-stack {
  opacity: 1;
  transform: translateY(0);
}

.project__section .container > div:nth-of-type(1) {
  grid-column: 1/3;
}

.project__section .container > div:nth-child(6) {
  grid-column: 3/5;
}

.project__section .container > div:nth-child(9) {
  grid-column: 3/5;
}

.project__section .container > div:nth-child(10) {
  grid-column: 1/3;
}

/* Fallback backgrounds when no image is provided */
.bg1:not([style*="background-image"]) {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
}

.bg1:not([style*="background-image"]) .content-wrapper {
  color: var(--text-color);
  text-shadow: none;
}

.bg1:not([style*="background-image"]) .project-title {
  color: var(--text-color) !important;
  text-shadow: none;
}

.bg1:not([style*="background-image"]) .project-label {
  color: var(--color-secondary-text) !important;
  text-shadow: none;
}

.bg2:not([style*="background-image"]) {
  background: linear-gradient(135deg, #f0f2f5 0%, #dde1e7 100%);
}

.bg2:not([style*="background-image"]) .content-wrapper {
  color: var(--text-color);
  text-shadow: none;
}

.bg2:not([style*="background-image"]) .project-title {
  color: var(--text-color) !important;
  text-shadow: none;
}

.bg2:not([style*="background-image"]) .project-label {
  color: var(--color-secondary-text) !important;
  text-shadow: none;
}

/* Override the general ::before for cards without background images */
.bg1:not([style*="background-image"])::before,
.bg2:not([style*="background-image"])::before {
  background: linear-gradient(
    135deg,
    transparent 0%,
    rgba(84, 98, 255, 0.05) 100%
  );
  opacity: 0.3;
}

.bg1:not([style*="background-image"]):hover::before,
.bg2:not([style*="background-image"]):hover::before {
  opacity: 0.6;
}

.project-overlay {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  padding: 10px;
  border-radius: 5px;
}

.project-overlay h3 {
  margin: 0;
  font-size: 20px;
}

.project-overlay p {
  margin: 5px 0 0;
  font-size: 14px;
}

/* Enhanced Modal Styles */
.modal-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.8) 100%
  );
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-background.open {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  padding: 0;
  border-radius: 20px;
  max-width: min(90vw, 800px);
  max-height: 90vh;
  overflow: hidden;
  display: grid;
  grid-template-columns: 1fr 1fr;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transform: scale(0.9) translateY(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.modal-background.open .modal-content {
  transform: scale(1) translateY(0);
}

.modal__image-container {
  position: relative;
  background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 20px 0 0 20px;
  min-height: 400px;
  height: 100%;
}

.modal__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal__image.loading {
  opacity: 0;
}

.loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.modal__image:hover {
  transform: scale(1.05);
}

.modal-info {
  padding: 2.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 1.5rem;
  background: white;
  border-radius: 0 20px 20px 0;
  position: relative;
}

.modal-info::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, #6366f1 100%);
  border-radius: 0 20px 0 0;
}

.modal-title {
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.modal__description {
  font-size: 1rem;
  color: var(--color-secondary-text);
  line-height: 1.6;
  margin: 0;
  text-align: left;
}

/* Enhanced Stack Tags */
.modal__stacks {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 0;
}

.modal__stacks .stack {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--color-primary);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  background: rgba(84, 98, 255, 0.1);
  border: 1px solid rgba(84, 98, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modal__stacks .stack::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.5s;
}

.modal__stacks .stack:hover {
  background: var(--color-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(84, 98, 255, 0.3);
}

.modal__stacks .stack:hover::before {
  left: 100%;
}

/* Enhanced Action Buttons */
.modal__actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.modal__git-link {
  padding: 0.75rem 1.5rem;
  color: white;
  text-decoration: none;
  border-radius: 12px;
  background: linear-gradient(
    135deg,
    var(--color-secondary-dark) 0%,
    #2d3748 100%
  );
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: none;
  cursor: pointer;
}

.modal__git-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-primary) 0%, #6366f1 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal__git-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.modal__git-link:hover::before {
  opacity: 1;
}

.modal__git-link i,
.modal__git-link span {
  position: relative;
  z-index: 1;
}

.modal__demo-link {
  padding: 0.75rem 1.5rem;
  color: var(--color-primary);
  text-decoration: none;
  border-radius: 12px;
  background: transparent;
  border: 2px solid var(--color-primary);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.modal__demo-link:hover {
  background: var(--color-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(84, 98, 255, 0.25);
}

/* Enhanced Project Card Interactions */
.project__section .container .project-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.project__section .container .project-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(84, 98, 255, 0.15);
}

.project__section .container .project-card:focus {
  outline: 3px solid var(--color-primary);
  outline-offset: 4px;
  transform: translateY(-4px);
}

.project__section .container .project-card:active {
  transform: translateY(-2px) scale(1.01);
  transition: transform 0.1s ease;
}

/* Content wrapper improvements */
.content-wrapper {
  position: relative;
  z-index: 3;
  transition: transform 0.3s ease;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  color: white;
}

.project__section .container > div:hover .content-wrapper {
  transform: translateY(-5px);
}

/* Ensure text is readable over background images */
.project-title {
  color: white !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.7);
  font-weight: 600;
}

.project-label {
  color: rgba(255, 255, 255, 0.9) !important;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.6);
  font-weight: 500;
}

/* Loading state for project cards */
.project-card.loading {
  opacity: 0.7;
  pointer-events: none;
}

.project-card.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--color-primary);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.modal-background {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-background.open {
  opacity: 1;
}

/* Enhanced Close Button */
.modal-close {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  z-index: 1001;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(10px);
  color: var(--color-secondary-text);
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-close:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
  transform: scale(1.1) rotate(90deg);
}

.modal-close:active {
  transform: scale(0.95) rotate(90deg);
}

/* Loading States */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(84, 98, 255, 0.1);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.modal__image.loading {
  opacity: 0;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  color: var(--color-secondary-text);
  font-size: 0.9rem;
  padding: 2rem;
}

.image-error::before {
  content: "🖼️";
  font-size: 3rem;
  opacity: 0.5;
}

/* Image Zoom Feature */
.modal__image-container.zoomable {
  cursor: zoom-in;
}

.modal__image-container.zoomed {
  cursor: zoom-out;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1002;
  background: rgba(0, 0, 0, 0.95);
  border-radius: 0;
}

.modal__image-container.zoomed .modal__image {
  max-width: 90vw;
  max-height: 90vh;
  width: auto;
  height: auto;
  object-fit: contain;
}

/* Project Stats */
.modal__stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(84, 98, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(84, 98, 255, 0.1);
}

.modal__stat {
  text-align: center;
}

.modal__stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-primary);
  display: block;
}

.modal__stat-label {
  font-size: 0.75rem;
  color: var(--color-secondary-text);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: 0.25rem;
}

/* Keyframe Animations */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes projectCardReveal {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes stackTagBounce {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.5);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  .project__section .container {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.25rem;
  }

  .project__section .container > div:nth-of-type(1) {
    grid-column: 1/3;
  }
}

/* This media query was conflicting with tablet styles - removed */

/* iPad Mini and smaller tablets - 2x3 Grid Layout */
@media (min-width: 768px) and (max-width: 820px) {
  .project__section .container {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 1rem;
    margin-top: 2rem;
    padding: 0 1rem;
    max-width: 100%;
    height: auto;
  }

  .project__section .container > div {
    height: 240px;
    border-radius: 12px;
    /* Remove hover animations for tablets */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Remove hover effects for tablets */
  .project__section .container > div:hover {
    transform: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .project__section .container > div:hover::before {
    opacity: 0.3;
  }

  .project__section .container > div:hover .content-wrapper {
    transform: none;
  }

  /* Reset all special grid positioning for clean 2x3 layout */
  .project__section .container > div:nth-of-type(1),
  .project__section .container > div:nth-child(6),
  .project__section .container > div:nth-child(9),
  .project__section .container > div:nth-child(10) {
    grid-column: span 1;
    height: 240px;
  }

  /* Apply same animations as large screens */
  .project__section .container > div {
    opacity: 0;
    animation: projectCardReveal 1s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  span.overlay-stack {
    animation: stackTagBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)
      forwards;
    animation-delay: calc(var(--i) * 0.1s + 0.3s);
    opacity: 0;
    transform: translateY(30px) scale(0.5);
  }

  /* Adjust text sizes for smaller tablets */
  .project__section h2 {
    font-size: clamp(2rem, 4vw, 2.4rem);
    margin-bottom: 1rem;
  }

  .project__section h2 span {
    font-size: clamp(1.4rem, 3vw, 1.8rem);
  }

  .project__section p {
    font-size: clamp(0.9rem, 1.5vw, 1rem);
    margin-bottom: 1.5rem;
  }
}

/* Larger tablets (iPad Pro, etc.) */
@media (min-width: 821px) and (max-width: 1024px) {
  .project__section .container {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-top: 2.5rem;
    padding: 0 1.5rem;
    max-width: 100%;
  }

  .project__section .container > div {
    height: 280px;
    border-radius: 16px;
    /* Remove hover animations for tablets */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Reset all special grid positioning for clean 2-column layout */
  .project__section .container > div:nth-of-type(1),
  .project__section .container > div:nth-child(6),
  .project__section .container > div:nth-child(9),
  .project__section .container > div:nth-child(10) {
    grid-column: span 1;
    height: 280px;
  }

  /* Remove hover effects for tablets - apply same as large screens */
  .project__section .container > div:hover {
    transform: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .project__section .container > div:hover::before {
    opacity: 0.3;
  }

  .project__section .container > div:hover .content-wrapper {
    transform: none;
  }

  /* Apply same animations as large screens */
  .project__section .container > div {
    opacity: 0;
    animation: projectCardReveal 1s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  span.overlay-stack {
    animation: stackTagBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)
      forwards;
    animation-delay: calc(var(--i) * 0.1s + 0.3s);
    opacity: 0;
    transform: translateY(30px) scale(0.5);
  }

  /* Overlay adjustments for tablets */
  .overlay {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .overlay-description {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.5;
  }

  span.overlay-stack {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    margin: 0.25rem;
    border-radius: 20px;
  }

  /* Modal optimizations for tablets */
  .modal-content {
    max-width: 90vw;
    max-height: 90vh;
    border-radius: 24px;
  }

  .modal-info {
    padding: 2.5rem 2rem;
    gap: 1.5rem;
  }

  .modal__actions {
    flex-direction: row;
    gap: 1.25rem;
    margin-top: 1.5rem;
  }

  .modal__git-link,
  .modal__demo-link {
    flex: 1;
    justify-content: center;
    padding: 0.9rem 1.5rem;
    font-size: 0.95rem;
  }

  .modal__stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    padding: 1.25rem;
  }

  .modal-title {
    font-size: 1.9rem;
    line-height: 1.3;
  }

  .modal__description {
    font-size: 1.05rem;
    line-height: 1.7;
  }

  .modal__stacks .stack {
    font-size: 0.85rem;
    padding: 0.6rem 1.1rem;
  }
  /* Additional tablet-specific improvements */
  .project__section .container > div::before {
    transition: opacity 0.4s ease;
  }

  .project__section .container > div:hover::before {
    opacity: 0.8;
  }

  /* Better text readability on tablets */
  .content-wrapper {
    padding: 1rem;
  }

  .project-title {
    font-size: clamp(1.1rem, 2.5vw, 1.3rem);
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .project-label {
    font-size: clamp(0.85rem, 2vw, 0.95rem);
    font-weight: 500;
  }

  /* Improved modal close button for tablets */
  .modal-close {
    width: 44px;
    height: 44px;
    font-size: 20px;
    top: 1.25rem;
    right: 1.25rem;
  }

  /* Better stack tag layout in modal */
  .modal__stacks {
    gap: 0.75rem;
    margin: 1.25rem 0;
  }
}

@media (max-width: 768px) {
  .project__section .container {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-top: 2rem;
  }

  .project__section .container > div {
    height: 180px;
    /* Remove hover animations for mobile */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Remove hover effects for mobile */
  .project__section .container > div:hover {
    transform: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .project__section .container > div:hover::before {
    opacity: 0.3;
  }

  .project__section .container > div:hover .content-wrapper {
    transform: none;
  }

  /* Apply same animations as large screens */
  .project__section .container > div {
    opacity: 0;
    animation: projectCardReveal 1s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  span.overlay-stack {
    animation: stackTagBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)
      forwards;
    animation-delay: calc(var(--i) * 0.1s + 0.3s);
    opacity: 0;
    transform: translateY(30px) scale(0.5);
  }

  .overlay {
    padding: 1rem;
  }

  .overlay-description {
    font-size: 0.8rem;
  }

  span.overlay-stack {
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
  }
}

@media (max-width: 576px) {
  .project__section .container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .project__section .container > div {
    height: 200px;
  }

  .project__section .container > div:nth-of-type(1) {
    grid-column: 1;
  }

  .project__section h2 {
    text-align: center;
  }
}

/* Mobile devices (300px and above) - Fixed layout */
@media (min-width: 300px) and (max-width: 480px) {
  .project__section .container {
    grid-template-columns: 1fr;
    gap: 0.75rem;
    margin-top: 1.5rem;
    padding: 0 0.75rem;
    max-width: 100%;
  }

  .project__section .container > div {
    height: 200px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    /* Remove hover animations for mobile */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Remove hover effects for mobile */
  .project__section .container > div:hover {
    transform: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .project__section .container > div:hover::before {
    opacity: 0.3;
  }

  .project__section .container > div:hover .content-wrapper {
    transform: none;
  }

  /* Apply same animations as large screens */
  .project__section .container > div {
    opacity: 0;
    animation: projectCardReveal 1s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  span.overlay-stack {
    animation: stackTagBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)
      forwards;
    animation-delay: calc(var(--i) * 0.1s + 0.3s);
    opacity: 0;
    transform: translateY(30px) scale(0.5);
  }

  /* Reset all special grid positioning for single column layout */
  .project__section .container > div:nth-of-type(1),
  .project__section .container > div:nth-child(6),
  .project__section .container > div:nth-child(9),
  .project__section .container > div:nth-child(10) {
    grid-column: 1;
    height: 200px;
  }

  span.overlay-stack {
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
    margin: 0.2rem;
    border-radius: 16px;
  }

  .overlay {
    padding: 0.75rem;
    border-radius: 12px;
  }

  .overlay-description {
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
  }

  .content-wrapper {
    padding: 0.75rem;
  }

  .project-title {
    font-size: clamp(1rem, 3.5vw, 1.2rem);
    margin-bottom: 0.3rem;
    font-weight: 600;
  }

  .project-label {
    font-size: clamp(0.8rem, 2.8vw, 0.9rem);
    font-weight: 500;
  }

  .project__section h2 {
    font-size: clamp(1.5rem, 5.5vw, 2rem);
    margin-bottom: 1rem;
    text-align: center;
  }

  .project__section h2 span {
    font-size: clamp(1.2rem, 4.2vw, 1.5rem);
  }

  .project__section p {
    font-size: clamp(0.85rem, 2.8vw, 0.95rem);
    margin-bottom: 1rem;
    text-align: center;
  }
}

/* Very small devices (below 300px) */
@media (max-width: 299px) {
  .project__section .container {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    margin-top: 1rem;
    padding: 0 0.5rem;
  }

  .project__section .container > div {
    height: 180px;
    border-radius: 10px;
    /* Remove hover animations for very small mobile */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Remove hover effects for very small mobile */
  .project__section .container > div:hover {
    transform: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .project__section .container > div:hover::before {
    opacity: 0.3;
  }

  .project__section .container > div:hover .content-wrapper {
    transform: none;
  }

  /* Apply same animations as large screens */
  .project__section .container > div {
    opacity: 0;
    animation: projectCardReveal 1s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  span.overlay-stack {
    animation: stackTagBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)
      forwards;
    animation-delay: calc(var(--i) * 0.1s + 0.3s);
    opacity: 0;
    transform: translateY(30px) scale(0.5);
  }

  /* Reset all special grid positioning for single column layout */
  .project__section .container > div:nth-of-type(1),
  .project__section .container > div:nth-child(6),
  .project__section .container > div:nth-child(9),
  .project__section .container > div:nth-child(10) {
    grid-column: 1;
    height: 180px;
  }

  span.overlay-stack {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
    margin: 0.15rem;
    border-radius: 14px;
  }

  .overlay {
    padding: 0.6rem;
    border-radius: 10px;
  }

  .overlay-description {
    font-size: 0.75rem;
    margin-bottom: 0.4rem;
    line-height: 1.3;
  }

  .content-wrapper {
    padding: 0.6rem;
  }

  .project-title {
    font-size: clamp(0.9rem, 4vw, 1.1rem);
    margin-bottom: 0.25rem;
  }

  .project-label {
    font-size: clamp(0.75rem, 3.2vw, 0.85rem);
  }

  .project__section h2 {
    font-size: clamp(1.3rem, 6.5vw, 1.7rem);
    margin-bottom: 0.8rem;
    text-align: center;
  }

  .project__section h2 span {
    font-size: clamp(1rem, 5vw, 1.3rem);
  }

  .project__section p {
    font-size: clamp(0.8rem, 3.2vw, 0.9rem);
    margin-bottom: 0.8rem;
    text-align: center;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .project__section .container > div,
  .overlay,
  span.overlay-stack,
  .content-wrapper {
    transition: none;
    animation: none;
    opacity: 1;
    transform: none;
  }
}

/* Modal Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    grid-template-columns: 1fr;
    max-width: 95vw;
    max-height: 95vh;
    overflow-y: auto;
  }

  .modal__image-container {
    border-radius: 20px 20px 0 0;
    min-height: 250px;
  }

  .modal-info {
    border-radius: 0 0 20px 20px;
    padding: 2rem 1.5rem;
  }

  .modal-info::before {
    border-radius: 20px 20px 0 0;
  }

  .modal__actions {
    flex-direction: column;
  }

  .modal__git-link,
  .modal__demo-link {
    justify-content: center;
    width: 100%;
  }

  .modal__stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Modal for mobile devices (300px and above) */
@media (min-width: 300px) and (max-width: 480px) {
  .modal-content {
    grid-template-columns: 1fr;
    max-width: 92vw;
    max-height: 90vh;
    margin: 1rem;
    border-radius: 16px;
  }

  .modal__image-container {
    border-radius: 16px 16px 0 0;
    min-height: 200px;
    max-height: 250px;
  }

  .modal-info {
    border-radius: 0 0 16px 16px;
    padding: 1.5rem 1rem;
    gap: 1rem;
  }

  .modal-info::before {
    border-radius: 16px 16px 0 0;
  }

  .modal-title {
    font-size: clamp(1.2rem, 4vw, 1.5rem);
    line-height: 1.3;
  }

  .modal__description {
    font-size: clamp(0.85rem, 2.8vw, 0.95rem);
    line-height: 1.5;
  }

  .modal__stacks {
    gap: 0.4rem;
    margin: 0.75rem 0;
  }

  .modal__stacks .stack {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
  }

  .modal__actions {
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1rem;
  }

  .modal__git-link,
  .modal__demo-link {
    justify-content: center;
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 0.85rem;
  }

  .modal__stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    padding: 1rem;
  }

  .modal__stat-value {
    font-size: 0.9rem;
  }

  .modal__stat-label {
    font-size: 0.7rem;
  }

  .modal-close {
    width: 36px;
    height: 36px;
    font-size: 16px;
    top: 1rem;
    right: 1rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .project__section .container > div {
    border: 2px solid var(--color-primary);
  }

  .overlay {
    background: rgba(0, 0, 0, 0.95);
  }

  span.overlay-stack {
    border: 2px solid white;
    background: var(--color-primary);
  }

  .modal-content {
    border: 2px solid var(--color-primary);
  }

  .modal__stacks .stack {
    border: 2px solid var(--color-primary);
    background: white;
  }
}

/* Print styles */
@media print {
  .modal-background {
    display: none;
  }
}
