/* Animation Keyframes */
@keyframes morphing {
  0% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  }
  50% {
    border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
  }
  100% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  }
}

@keyframes floatAnimation {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes spin {
  to {
    transform: rotate(1turn);
  }
}

@keyframes slideTextDown {
  0% {
    -webkit-transform: translateY(-20%);
    transform: translateY(-20%);
    opacity: 0;
  }
  40% {
    -webkit-transform: translateY(-10%);
    transform: translateY(-10%);
  }
  100% {
    -webkit-transform: translateY(0%);
    transform: translateY(0%);
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
    opacity: 0;
  }
  30% {
    -webkit-transform: translate3d(0, 5px, 0);
    transform: translate3d(0, 10%, 0);
    opacity: 0;
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  0% {
    -webkit-transform: translateX(120px) scale(0.8);
    transform: translateX(120px) scale(0.8);
    opacity: 0;
    filter: blur(10px);
  }
  60% {
    -webkit-transform: translateX(-10px) scale(1.02);
    transform: translateX(-10px) scale(1.02);
    opacity: 0.8;
    filter: blur(2px);
  }
  100% {
    -webkit-transform: translateX(0) scale(1);
    transform: translateX(0) scale(1);
    opacity: 1;
    filter: blur(0);
  }
}

@keyframes slideInLeft {
  0% {
    -webkit-transform: translateX(-10%);
    transform: translateX(-10%);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes heartbeat {
  from {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  10% {
    -webkit-transform: scale(0.91);
    transform: scale(0.91);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  17% {
    -webkit-transform: scale(0.98);
    transform: scale(0.98);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  33% {
    -webkit-transform: scale(0.87);
    transform: scale(0.87);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  45% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}

@keyframes rotate-in-center {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotateZ(0deg);
  }
  50% {
    -webkit-transform: rotateZ(180deg);
    transform: rotate(180deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes backgroundAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-in {
  animation: slideInRight 1s cubic-bezier(0.16, 1, 0.3, 1) 0.2s both;
}

.slide-out {
  animation: slide-out 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.5s both;
}

@keyframes slide-out {
  100% {
    -webkit-transform: translateX(100px);
    transform: translateX(100px);
    opacity: 0;
  }
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}

/* Project Card Animations */
@keyframes projectCardReveal {
  0% {
    opacity: 0;
    transform: translateY(80px) scale(0.8) rotateX(25deg);
    filter: blur(8px);
  }
  50% {
    opacity: 0.6;
    transform: translateY(20px) scale(0.95) rotateX(5deg);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
    filter: blur(0);
  }
}

@keyframes projectCardHover {
  0% {
    transform: translateY(-8px) rotateY(5deg) rotateX(2deg) scale(1.05);
  }
  25% {
    transform: translateY(-10px) rotateY(3deg) rotateX(1deg) scale(1.06);
  }
  50% {
    transform: translateY(-6px) rotateY(7deg) rotateX(3deg) scale(1.04);
  }
  75% {
    transform: translateY(-9px) rotateY(2deg) rotateX(1deg) scale(1.055);
  }
  100% {
    transform: translateY(-8px) rotateY(5deg) rotateX(2deg) scale(1.05);
  }
}

@keyframes stackTagBounce {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.5);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.1);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInFromBottom {
  0% {
    transform: translateY(100px) scale(0.9);
    opacity: 0;
  }
  60% {
    transform: translateY(-5px) scale(1.02);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.cookie-consent.slide-in {
  animation: slideInFromBottom 0.8s cubic-bezier(0.16, 1, 0.3, 1) 0.5s both;
}
